"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { format } from "date-fns";
import Link from "next/link";
import { Eye, UserPlus, ExternalLink } from "lucide-react";
import { EditablePriorityCell } from "./editable-priority-cell";

export const createApplicationsColumns = (
  onPriorityUpdate: (
    applicationId: string,
    newPriority: string
  ) => Promise<void>,
  onAssignAgent: (application: IApplication) => void,
  userRole?: "user" | "admin" | "agent"
): ColumnDef<IApplication>[] => [
  {
    id: "application_number",
    header: "Application #",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="font-medium w-[150px]">
          <Link
            href={`/applications/${application.id}`}
            className="text-primary hover:underline flex items-center gap-1"
          >
            {application.application_number}
            <ExternalLink className="h-3 w-3" />
          </Link>
        </div>
      );
    },
  },
  {
    id: "service_name",
    header: "Service Name",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="font-medium">{application.service_name || "N/A"}</div>
      );
    },
  },
  {
    id: "status",
    header: "Status",
    cell: ({ row }) => {
      const application = row.original;

      // Calculate status based on current_step and numberOfSteps
      let computedStatus = "Pending";
      let statusVariant: "default" | "secondary" | "outline" | "destructive" =
        "outline";

      // If numberOfSteps is available, use it for computation
      if (application.numberOfSteps && application.numberOfSteps > 0) {
        if (application.current_step >= application.numberOfSteps) {
          computedStatus = "Completed";
          statusVariant = "default";
        } else {
          computedStatus = "Pending";
          statusVariant = "secondary";
        }
      } else if (application.steps && Array.isArray(application.steps)) {
        // Fallback: use steps array length
        const totalSteps = application.steps.length;
        if (totalSteps > 0 && application.current_step >= totalSteps) {
          computedStatus = "Completed";
          statusVariant = "default";
        } else {
          computedStatus = "Pending";
          statusVariant = "secondary";
        }
      } else {
        // Fallback to original status if no step information is available
        computedStatus = application.status;
        switch (application.status.toLowerCase()) {
          case "completed":
            statusVariant = "default";
            break;
          case "in_progress":
            statusVariant = "secondary";
            break;
          case "pending":
            statusVariant = "outline";
            break;
          case "rejected":
            statusVariant = "destructive";
            break;
          default:
            statusVariant = "outline";
        }
      }

      return (
        <Badge variant={statusVariant} className="capitalize">
          {computedStatus}
        </Badge>
      );
    },
  },
  {
    id: "priority",
    header: "Priority",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <EditablePriorityCell
          applicationId={application.id}
          currentPriority={application.priority_level}
          onPriorityUpdate={onPriorityUpdate}
        />
      );
    },
  },

  {
    id: "assigned_agent",
    header: "Assigned Agent",
    cell: ({ row }) => {
      const application = row.original;
      if (!application.assigned_agent) {
        return (
          <span className="text-muted-foreground text-sm">Unassigned</span>
        );
      }

      // Handle both single agent and array of agents
      const agents = Array.isArray(application.assigned_agent)
        ? application.assigned_agent
        : [application.assigned_agent];

      if (agents.length === 0) {
        return (
          <span className="text-muted-foreground text-sm">Unassigned</span>
        );
      }

      if (agents.length === 1) {
        return (
          <div className="flex flex-col">
            <span className="font-medium text-sm">{agents[0].name}</span>
            <span className="text-xs text-muted-foreground">
              {agents[0].email}
            </span>
          </div>
        );
      }

      // Multiple agents - show count and first agent
      return (
        <div className="flex flex-col">
          <span className="font-medium text-sm">
            {agents[0].name} +{agents.length - 1} more
          </span>
          <span className="text-xs text-muted-foreground">
            {agents.length} agents assigned
          </span>
        </div>
      );
    },
  },
  {
    id: "customer_name",
    header: "Name",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div>
          {(application as any).guest_name ||
            application.guest?.name ||
            (application as any).user?.name ||
            "N/A"}
        </div>
      );
    },
  },
  {
    id: "customer_email",
    header: "Email",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div>
          {(application as any).guest_email ||
            application.guest?.email ||
            (application as any).user?.email ||
            "N/A"}
        </div>
      );
    },
  },
  {
    id: "customer_mobile",
    header: "Mobile",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div>
          {(application as any).guest_mobile ||
            application.guest?.mobile ||
            (application as any).user?.mobile ||
            "N/A"}
        </div>
      );
    },
  },
  {
    id: "created_at",
    header: "Created",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.created_at), "MMM dd, yyyy")}
      </div>
    ),
  },
  {
    id: "updated_at",
    header: "Updated",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.updated_at), "MMM dd, yyyy")}
      </div>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="flex items-center gap-1 w-[120px]">
          {/* Show Assign Agent button only for admin users */}
          {userRole === "admin" && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAssignAgent(application)}
              className="h-8 w-8 p-0"
              title="Assign Agent"
            >
              <UserPlus className="h-4 w-4" />
              <span className="sr-only">Assign agent</span>
            </Button>
          )}
          {/* Show View button for all users */}
          <Button variant="ghost" size="sm" asChild className="h-8 w-8 p-0">
            <Link href={`/applications/${application.id}`}>
              <Eye className="h-4 w-4" />
              <span className="sr-only">View application details</span>
            </Link>
          </Button>
        </div>
      );
    },
  },
];
